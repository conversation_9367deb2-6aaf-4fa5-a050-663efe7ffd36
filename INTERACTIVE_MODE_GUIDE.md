# Interactive Mode Guide

The interactive mode lets you manually enter data and explore the hackathon platform step by step. It's like being the admin of the contest!

## How to start

```bash
./build.sh
# Choose option 3: Interactive Mode
```

Or directly:
```bash
java -cp out InteractiveMode
```

## What you can do

### 1. 📝 Add Problem
Create your own coding problems with:
- Unique ID and name
- Description 
- Difficulty level (EASY, MEDIUM, HARD)
- Score points
- Tags (comma-separated)

**Example:**
```
Problem ID: ARRAY_001
Problem Name: Two Sum Problem
Problem Description: Find two numbers that add up to target
Difficulty: EASY
Score: 100
Tags: array, hash-table, easy
```

### 2. 👤 Register User
Add contestants with:
- User ID
- Full name
- Department

**Example:**
```
User ID: john_doe
User Name: <PERSON>
Department: Engineering
```

### 3. 📋 View Problems
Browse problems with filtering and sorting:
- **All problems** - see everything
- **Filter by difficulty** - only EASY, MEDIUM, or HARD
- **Filter by tag** - problems with specific tags
- **Sort options** - by score, difficulty, etc.

### 4. 🚀 Solve Problem
Simulate solving a problem:
- Enter your user ID
- Choose problem ID to solve
- Enter time taken (in seconds)
- Get your score and recommendations!

### 5. ✅ View Solved Problems
See what problems any user has completed.

### 6. 🏆 View Leaderboard
Check who's currently winning the contest.

### 7. ⭐ View Top Problems by Tag
Find the most popular problems in any category.

### 8. 📊 View Statistics
See platform stats - total users, problems, submissions.

## Sample walkthrough

Here's what a typical session might look like:

```
1. Add a few problems (different difficulties)
2. Register 2-3 users
3. Have users solve some problems
4. Check the leaderboard
5. View recommendations
6. See statistics
```

## Tips for testing

- **Start simple**: Add 1-2 easy problems first
- **Register multiple users**: More fun to see competition
- **Try different solve times**: See how scoring changes
- **Use varied tags**: Test the recommendation engine
- **Check edge cases**: Try invalid inputs to see error handling

## Example session

```
=== Add some problems ===
Problem 1: EASY_001, "Hello World", EASY, 50 points, tags: basic,string
Problem 2: MED_001, "Binary Search", MEDIUM, 150 points, tags: search,array
Problem 3: HARD_001, "Graph Traversal", HARD, 300 points, tags: graph,dfs

=== Register users ===
User 1: alice, "Alice Johnson", "Engineering"  
User 2: bob, "Bob Smith", "Product"

=== Solve problems ===
Alice solves EASY_001 in 60 seconds → Gets score + recommendations
Bob solves MED_001 in 180 seconds → Gets different score due to time
Alice solves MED_001 in 120 seconds → Faster = higher score

=== Check results ===
View leaderboard → See who's winning
View Alice's solved problems → See her progress
View top problems by "array" tag → See popular array problems
```

## Error handling

The system handles common mistakes gracefully:
- Duplicate IDs (problems/users)
- Invalid difficulty levels
- Non-existent users/problems
- Solving same problem twice
- Invalid input formats

Try entering wrong data to see the error messages!

## Why this is useful

Interactive mode lets you:
- **Understand the system** by using it hands-on
- **Test edge cases** with your own data
- **See how algorithms work** with different inputs
- **Validate features** work as expected
- **Experience the user journey** from both admin and contestant perspectives

It's basically a manual testing environment where you control everything!

---

*Pro tip: Start with the demo mode first to see sample data, then try interactive mode to create your own scenarios.*
