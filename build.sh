#!/bin/bash

# PhonePe Hackathon Platform Build Script
echo "=== PhonePe Hackathon Platform Build Script ==="

# Clean previous build
echo "Cleaning previous build..."
rm -rf out
mkdir -p out

# Compile all Java files
echo "Compiling Java files..."
find src -name "*.java" -exec javac -d out {} +

if [ $? -eq 0 ]; then
    echo "✓ Compilation successful!"
    
    echo ""
    echo "=== Available Commands ==="
    echo "1. Run Demo:           java -cp out Main"
    echo "2. Run Tests:          java -cp out tests.HackathonServiceTest"
    echo "3. Interactive Mode:   java -cp out InteractiveMode"
    echo ""

    # Ask user what to run
    read -p "What would you like to run? (1 for Demo, 2 for Tests, 3 for Interactive, Enter to skip): " choice
    
    case $choice in
        1)
            echo ""
            echo "=== Running Demo ==="
            java -cp out Main
            ;;
        2)
            echo ""
            echo "=== Running Tests ==="
            java -cp out tests.HackathonServiceTest
            ;;
        3)
            echo ""
            echo "=== Starting Interactive Mode ==="
            java -cp out InteractiveMode
            ;;
        *)
            echo "Build completed. Use the commands above to run the application."
            ;;
    esac
else
    echo "✗ Compilation failed!"
    exit 1
fi
