# PhonePe Hackathon Platform

Hey! This is my solution for the PhonePe machine coding round. Built a backend system for their hackathon platform where contestants can solve problems and compete for scores.

## What it does

Basically, it's like a mini LeetCode for PhonePe's internal hackathon. Users can:
- Register for the contest
- Browse and filter problems 
- Solve problems and get scores
- See recommendations for next problems
- Check leaderboards

## How I built it

Used plain Java with in-memory storage (HashMap/ConcurrentHashMap). Kept it simple but made sure it's thread-safe since multiple users might be using it simultaneously.

### Main components:
- **Models**: Problem, User, Submission - basic data classes
- **Repositories**: Handle data storage and retrieval 
- **Services**: Main business logic lives here
- **Strategies**: Different algorithms for scoring and recommendations

## Key features I implemented

### Basic stuff (mandatory):
- Add problems with difficulty levels and tags
- User registration 
- Filter problems by difficulty/tags
- Sort problems by score, difficulty etc.
- Solve problems and track time
- Get user's solved problems
- Show problem statistics (how many solved it, average time)
- Leaderboard functionality
- Top liked problems by category

### Extra features:
- Smart recommendations after solving a problem
- Different scoring strategies (simple vs time-based)
- Extensible design for adding new algorithms

## Scoring system

I implemented two approaches:
1. **Simple**: Just give the problem's base score
2. **Time-based**: Bonus for fast solutions, penalty for very slow ones

The time-based one considers difficulty too - harder problems get higher multipliers.

## Recommendation engine

After you solve a problem, it suggests 5 similar ones based on:
- Similar tags (40% weight)
- Appropriate difficulty progression (30% weight) 
- Problem popularity (30% weight)

## How to run

```bash
# Compile everything
javac -d out src/**/*.java

# Run the demo
java -cp out Main

# Run tests
java -cp out tests.HackathonServiceTest
```

Or just use the build script:
```bash
./build.sh
```

## Testing

Wrote 38 test cases covering:
- All the main features
- Error scenarios (invalid inputs, edge cases)
- Basic concurrency testing

All tests pass, which is nice!

## Design decisions

### Why in-memory storage?
- Requirements said to use in-memory data structures
- ConcurrentHashMap for thread safety
- Fast for a coding round, easy to test

### Why Strategy pattern?
- Makes it easy to add new scoring algorithms
- Can switch between different recommendation strategies
- Follows open/closed principle

### Why Repository pattern?
- Clean separation between data access and business logic
- Easy to mock for testing
- Could easily swap to database later

## What I learned

This was a good exercise in:
- Designing extensible systems
- Balancing simplicity vs flexibility
- Thread-safe programming
- Writing comprehensive tests

The trickiest part was getting the recommendation algorithm right. Had to balance between being too simple (just tag matching) and too complex (machine learning overkill).

## If I had more time

Would probably add:
- REST API layer
- Database integration
- Real-time leaderboard updates
- More sophisticated recommendation algorithms
- Better analytics and insights

## File structure

```
src/
├── Main.java                    # Demo with sample data
├── models/                      # Data classes
├── repositories/                # Data access layer  
├── services/                    # Business logic
├── strategies/                  # Pluggable algorithms
├── enums/                       # Constants and types
├── exceptions/                  # Custom error handling
└── tests/                       # Test suite
```

Pretty straightforward organization. Tried to keep related stuff together and maintain clear boundaries between layers.

## Performance

Should handle a decent number of users concurrently. Used efficient data structures and algorithms:
- O(1) for most add/lookup operations
- O(n log n) for sorting (can't do better than that)
- O(n) for filtering and recommendations

Memory usage scales linearly with data size, which is expected.

## Final thoughts

Overall happy with how it turned out. The code is clean, well-tested, and extensible. Follows good OOP principles without being over-engineered.

The system could realistically be used for a small to medium hackathon with some additional work (database, API layer, UI). The core logic is solid and the architecture supports scaling up.

---

*Built for PhonePe Machine Coding Round*  
*Time taken: ~4 hours*  
*Lines of code: ~2500 (including tests and comments)*
