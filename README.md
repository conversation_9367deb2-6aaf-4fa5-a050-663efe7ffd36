# PhonePe Hackathon Platform Backend

A comprehensive backend system for hosting PhonePe's annual hackathon, designed to support a 2-day competitive programming event where contestants maximize their scores by solving problems of varying difficulty levels.

## 🏗️ Architecture Overview

### Design Principles Applied
- **Single Responsibility Principle**: Each class has a single, well-defined purpose
- **Open/Closed Principle**: Extensible design using Strategy pattern for scoring and recommendations
- **Dependency Inversion**: Service layer depends on abstractions, not concrete implementations
- **Separation of Concerns**: Clear separation between data, business logic, and presentation layers

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │   Service       │    │   Data          │
│   Layer         │    │   Layer         │    │   Layer         │
│                 │    │                 │    │                 │
│ • Main.java     │───▶│ • HackathonSvc  │───▶│ • Repositories  │
│ • Demo/Tests    │    │ • Strategies    │    │ • Models        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Project Structure

```
src/
├── Main.java                          # Entry point with comprehensive demo
├── models/                            # Domain models
│   ├── Problem.java                   # Problem entity with Builder pattern
│   ├── User.java                      # User entity
│   ├── Submission.java                # Submission tracking
│   ├── ProblemStats.java              # Problem statistics
│   └── ProblemWithStats.java          # DTO for problem display
├── enums/                             # Type-safe enumerations
│   ├── Difficulty.java                # Problem difficulty levels
│   ├── SortCriteria.java              # Sorting options
│   └── FilterCriteria.java            # Filtering options
├── repositories/                      # Data access layer
│   ├── ProblemRepository.java         # Problem data management
│   ├── UserRepository.java            # User data management
│   └── SubmissionRepository.java      # Submission data management
├── services/                          # Business logic layer
│   └── HackathonService.java          # Main service orchestrator
├── strategies/                        # Strategy pattern implementations
│   ├── ScoringStrategy.java           # Scoring algorithm interface
│   ├── ScoringStrategyFactory.java    # Factory for scoring strategies
│   ├── RecommendationStrategy.java    # Recommendation algorithm interface
│   └── RecommendationStrategyFactory.java # Factory for recommendation strategies
├── exceptions/                        # Custom exception handling
│   └── CustomExceptions.java          # Domain-specific exceptions
└── tests/                            # Comprehensive test suite
    └── HackathonServiceTest.java      # Unit and integration tests

Additional Files:
├── README.md                          # Comprehensive documentation
├── build.sh                           # Build and run script
└── out/                              # Compiled classes directory
```

## 🚀 Features Implemented

### Mandatory Features ✅
1. **Problem Management**: Add problems with attributes (description, tags, difficulty, score)
2. **User Registration**: Register contestants with name and department
3. **Problem Filtering & Sorting**: Filter by difficulty/tags, sort by multiple criteria
4. **Problem Solving**: Track submissions with time and calculated scores
5. **User Progress**: Fetch solved problems for any user
6. **Problem Statistics**: View solve count and average time for problems
7. **Leaderboard**: Get current contest leader
8. **Problem Curation**: Top N most liked problems by tag

### Extension Features ✅
1. **Smart Recommendations**: Get top 5 relevant problems after solving
2. **Extensible Strategies**: Pluggable scoring and recommendation algorithms
3. **Advanced Analytics**: Comprehensive problem and user statistics

## 🧠 Design Decisions & Rationale

### 1. Data Storage Strategy
**Chosen Approach**: In-memory storage using `ConcurrentHashMap`
- **Why**: Thread-safe, high performance, meets requirements
- **Alternatives Considered**:
  - HashMap: Not thread-safe
  - Database: Overkill for machine coding round
  - File storage: Slower, more complex

### 2. Scoring Strategy Pattern
**Chosen Approach**: Strategy pattern with multiple algorithms
- **Implementation**: 
  - `SimpleScoringStrategy`: Awards base problem score
  - `TimeBonusScoringStrategy`: Considers time with bonus/penalty system
- **Why**: Extensible, testable, follows Open/Closed principle
- **Formula**: `baseScore × difficultyMultiplier × timeMultiplier`

### 3. Recommendation System
**Chosen Approach**: Hybrid strategy combining multiple factors
- **Factors Considered**:
  - Tag similarity (40% weight)
  - Difficulty progression (30% weight)  
  - Problem popularity (30% weight)
- **Why**: More intelligent than simple tag matching
- **Alternatives**: Pure tag-based, collaborative filtering

### 4. Repository Pattern
**Chosen Approach**: Separate repositories for each entity
- **Benefits**: 
  - Clear separation of concerns
  - Easy to test and mock
  - Consistent data access patterns
- **Thread Safety**: All repositories use `ConcurrentHashMap`

### 5. Builder Pattern for Problem Creation
**Chosen Approach**: Builder pattern for complex Problem objects
- **Why**: 
  - Immutable objects
  - Validation at build time
  - Readable object creation
  - Extensible for future attributes

## 🔧 Technical Implementation Details

### Thread Safety
- All repositories use `ConcurrentHashMap` for thread-safe operations
- Immutable domain objects where possible
- Atomic operations for statistics updates

### Performance Optimizations
- Indexed data structures for fast lookups
- Stream API for efficient filtering and sorting
- Lazy evaluation where appropriate

### Error Handling
- Custom exceptions for domain-specific errors
- Comprehensive validation at service boundaries
- Graceful error messages for user feedback

### Extensibility Points
1. **Scoring Strategies**: Add new algorithms by implementing `ScoringStrategy`
2. **Recommendation Strategies**: Add new algorithms by implementing `RecommendationStrategy`
3. **Filter Criteria**: Extend `FilterCriteria` enum for new filter types
4. **Sort Criteria**: Extend `SortCriteria` enum for new sorting options

## 📊 Complexity Analysis

### Time Complexity
- **Add Problem/User**: O(1)
- **Solve Problem**: O(1) for submission + O(n) for recommendations
- **Fetch Problems**: O(n) for filtering + O(n log n) for sorting
- **Get Leader**: O(n) where n = number of users
- **Top N Problems**: O(m log m) where m = problems with specific tag

### Space Complexity
- **Overall**: O(P + U + S) where P=problems, U=users, S=submissions
- **Indexes**: Additional O(U×P) for user-problem mappings

## 🧪 Testing Strategy

### Test Coverage
- **Unit Tests**: All service methods
- **Integration Tests**: End-to-end workflows
- **Error Handling**: Exception scenarios
- **Edge Cases**: Boundary conditions
- **Concurrency**: Basic thread safety

### Test Categories
1. **Functional Tests**: Core feature validation
2. **Error Handling Tests**: Exception scenarios
3. **Performance Tests**: Basic load testing
4. **Strategy Tests**: Different algorithm combinations

## 🚀 How to Run

### Prerequisites
- Java 8 or higher
- No external dependencies required

### Running the Demo

#### Option 1: Using Build Script (Recommended)
```bash
# Make script executable and run
chmod +x build.sh
./build.sh

# Follow the interactive prompts to run demo or tests
```

#### Option 2: Manual Commands
```bash
# Compile all Java files
javac -d out src/**/*.java

# Run the main demo
java -cp out Main

# Run the test suite
java -cp out tests.HackathonServiceTest
```

### Expected Output
The demo will showcase:
1. Problem and user registration
2. Problem filtering and sorting
3. Problem solving with recommendations
4. Leaderboard functionality
5. Top problems by tag

## 🔮 Future Enhancements

### Scalability Improvements
1. **Database Integration**: Replace in-memory storage
2. **Caching Layer**: Redis for frequently accessed data
3. **Microservices**: Split into problem, user, and submission services
4. **Event Sourcing**: Track all state changes

### Feature Extensions
1. **Real-time Updates**: WebSocket for live leaderboard
2. **Team Competitions**: Support for team-based contests
3. **Problem Difficulty Adjustment**: Dynamic difficulty based on solve rates
4. **Advanced Analytics**: Detailed performance insights

### Performance Optimizations
1. **Pagination**: For large result sets
2. **Async Processing**: Non-blocking operations
3. **Connection Pooling**: For database operations
4. **Load Balancing**: Horizontal scaling support

## 📈 Monitoring & Metrics

### Key Metrics to Track
- Problem solve rates by difficulty
- Average time per problem
- User engagement patterns
- System performance metrics

### Logging Strategy
- Structured logging for all operations
- Error tracking and alerting
- Performance monitoring
- User activity analytics

## 🤝 Contributing

### Code Standards
- Follow Java naming conventions
- Maintain test coverage above 80%
- Document all public APIs
- Use meaningful commit messages

### Adding New Features
1. Implement interface/strategy if applicable
2. Add comprehensive tests
3. Update documentation
4. Ensure backward compatibility

---

## 📞 Support & Questions

For any questions about the implementation or design decisions, please refer to:
- Code comments for detailed explanations
- Test cases for usage examples
- This README for architectural decisions

**Author**: Shivam Shroti  
**Date**: 2025-07-29  
**Version**: 1.0.0
