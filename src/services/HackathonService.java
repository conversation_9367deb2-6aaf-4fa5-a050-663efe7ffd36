package services;

import models.*;
import enums.*;
import exceptions.CustomExceptions;
import repositories.*;
import strategies.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Main service class for the hackathon platform
 * Implements all required functionalities with proper separation of concerns
 */
public class HackathonService {
    private final ProblemRepository problemRepository;
    private final UserRepository userRepository;
    private final SubmissionRepository submissionRepository;
    private final ScoringStrategy scoringStrategy;
    private final RecommendationStrategy recommendationStrategy;

    public HackathonService() {
        this.problemRepository = new ProblemRepository();
        this.userRepository = new UserRepository();
        this.submissionRepository = new SubmissionRepository();
        this.scoringStrategy = ScoringStrategyFactory.createStrategy(ScoringStrategyFactory.StrategyType.TIME_BONUS);
        this.recommendationStrategy = RecommendationStrategyFactory.createStrategy(RecommendationStrategyFactory.StrategyType.HYBRID);
    }

    public HackathonService(ScoringStrategyFactory.StrategyType scoringType, 
                           RecommendationStrategyFactory.StrategyType recommendationType) {
        this.problemRepository = new ProblemRepository();
        this.userRepository = new UserRepository();
        this.submissionRepository = new SubmissionRepository();
        this.scoringStrategy = ScoringStrategyFactory.createStrategy(scoringType);
        this.recommendationStrategy = RecommendationStrategyFactory.createStrategy(recommendationType);
    }

    /**
     * Add a new problem to the platform
     */
    public void addProblem(String id, String name, String description, Set<String> tags, 
                          Difficulty difficulty, int score) {
        try {
            Problem problem = new Problem.Builder()
                    .setId(id)
                    .setName(name)
                    .setDescription(description)
                    .setTags(tags)
                    .setDifficulty(difficulty)
                    .setScore(score)
                    .build();
            
            problemRepository.addProblem(problem);
            System.out.println("Problem added successfully: " + problem.getName());
        } catch (Exception e) {
            System.err.println("Error adding problem: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Register a new user
     */
    public void addUser(String id, String name, String department) {
        try {
            User user = new User(id, name, department);
            userRepository.addUser(user);
            System.out.println("User registered successfully: " + user.getName());
        } catch (Exception e) {
            System.err.println("Error registering user: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Fetch problems with filtering and sorting
     */
    public List<ProblemWithStats> fetchProblems(Map<FilterCriteria, Object> filters, 
                                               SortCriteria sortCriteria) {
        try {
            List<Problem> problems = problemRepository.getAllProblems();
            
            // Apply filters
            if (filters != null && !filters.isEmpty()) {
                problems = applyFilters(problems, filters);
            }
            
            // Convert to ProblemWithStats
            List<ProblemWithStats> problemsWithStats = problems.stream()
                    .map(this::convertToProblemWithStats)
                    .collect(Collectors.toList());
            
            // Apply sorting
            if (sortCriteria != null) {
                problemsWithStats = applySorting(problemsWithStats, sortCriteria);
            }
            
            return problemsWithStats;
        } catch (Exception e) {
            System.err.println("Error fetching problems: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Solve a problem and return recommendations
     */
    public List<Problem> solve(String userId, String problemId, long timeTakenInSeconds) {
        try {
            // Validate user and problem exist
            User user = userRepository.getUserById(userId);
            Problem problem = problemRepository.getProblemById(problemId);
            
            // Calculate score using strategy
            int calculatedScore = scoringStrategy.calculateScore(problem, timeTakenInSeconds);
            
            // Create submission
            String submissionId = generateSubmissionId(userId, problemId);
            Submission submission = new Submission(submissionId, userId, problemId, 
                                                 timeTakenInSeconds, calculatedScore);
            
            submissionRepository.addSubmission(submission);
            problemRepository.updateProblemStats(problemId, timeTakenInSeconds);
            
            System.out.println("Problem solved successfully! Score: " + calculatedScore);
            
            // Get recommendations
            List<Problem> allProblems = problemRepository.getAllProblems();
            List<Submission> userSubmissions = submissionRepository.getSubmissionsByUser(userId);
            Map<String, ProblemStats> problemStats = problemRepository.getAllProblemStats();
            
            return recommendationStrategy.recommend(problem, allProblems, userSubmissions, problemStats, 5);
            
        } catch (Exception e) {
            System.err.println("Error solving problem: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Fetch solved problems for a user
     */
    public List<ProblemWithStats> fetchSolvedProblems(String userId) {
        try {
            userRepository.getUserById(userId); // Validate user exists
            
            Set<String> solvedProblemIds = submissionRepository.getProblemsSolvedByUser(userId);
            
            return solvedProblemIds.stream()
                    .map(problemRepository::getProblemById)
                    .map(this::convertToProblemWithStats)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            System.err.println("Error fetching solved problems: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Get the current leader
     */
    public User getLeader() {
        try {
            List<Map.Entry<String, Integer>> rankings = submissionRepository.getUserRankings();
            
            if (rankings.isEmpty()) {
                throw new CustomExceptions.NoDataAvailableException("No submissions found");
            }
            
            String leaderId = rankings.get(0).getKey();
            return userRepository.getUserById(leaderId);
            
        } catch (Exception e) {
            System.err.println("Error getting leader: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Get top N most liked problems of a certain tag
     */
    public List<ProblemWithStats> getTopNProblems(String tag, int n) {
        try {
            if (tag == null || tag.trim().isEmpty()) {
                throw new IllegalArgumentException("Tag cannot be null or empty");
            }
            if (n <= 0) {
                throw new IllegalArgumentException("N must be positive");
            }
            
            List<Problem> problemsWithTag = problemRepository.getProblemsByTag(tag);
            
            return problemsWithTag.stream()
                    .map(this::convertToProblemWithStats)
                    .sorted((p1, p2) -> Integer.compare(p2.getLikeCount(), p1.getLikeCount()))
                    .limit(n)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            System.err.println("Error getting top problems: " + e.getMessage());
            throw e;
        }
    }

    // Helper methods
    private List<Problem> applyFilters(List<Problem> problems, Map<FilterCriteria, Object> filters) {
        return problems.stream()
                .filter(problem -> {
                    for (Map.Entry<FilterCriteria, Object> filter : filters.entrySet()) {
                        if (!matchesFilter(problem, filter.getKey(), filter.getValue())) {
                            return false;
                        }
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }

    private boolean matchesFilter(Problem problem, FilterCriteria criteria, Object value) {
        switch (criteria) {
            case DIFFICULTY:
                return problem.getDifficulty() == value;
            case TAG:
                return problem.getTags().contains(value.toString());
            case SCORE_RANGE:
                int[] scoreRange = (int[]) value;
                return problem.getScore() >= scoreRange[0] && problem.getScore() <= scoreRange[1];
            default:
                return true;
        }
    }

    private List<ProblemWithStats> applySorting(List<ProblemWithStats> problems, SortCriteria sortCriteria) {
        Comparator<ProblemWithStats> comparator = getComparator(sortCriteria);
        return problems.stream()
                .sorted(comparator)
                .collect(Collectors.toList());
    }

    private Comparator<ProblemWithStats> getComparator(SortCriteria sortCriteria) {
        Comparator<ProblemWithStats> comparator;
        
        switch (sortCriteria.getField()) {
            case "score":
                comparator = Comparator.comparing(p -> p.getProblem().getScore());
                break;
            case "difficulty":
                comparator = Comparator.comparing(p -> p.getProblem().getDifficulty());
                break;
            case "solveCount":
                comparator = Comparator.comparing(ProblemWithStats::getSolveCount);
                break;
            case "averageTime":
                comparator = Comparator.comparing(ProblemWithStats::getAverageTime);
                break;
            case "likeCount":
                comparator = Comparator.comparing(ProblemWithStats::getLikeCount);
                break;
            default:
                comparator = Comparator.comparing(p -> p.getProblem().getName());
        }
        
        return sortCriteria.isAscending() ? comparator : comparator.reversed();
    }

    private ProblemWithStats convertToProblemWithStats(Problem problem) {
        ProblemStats stats = problemRepository.getProblemStats(problem.getId());
        return new ProblemWithStats(problem, stats.getSolveCount(), 
                                   stats.getAverageTimeInSeconds(), stats.getLikeCount());
    }

    private String generateSubmissionId(String userId, String problemId) {
        return userId + "_" + problemId + "_" + System.currentTimeMillis();
    }

    // Utility methods for testing and management
    public void addLikeToProblem(String problemId) {
        problemRepository.addLikeToProblem(problemId);
    }

    public int getTotalUsersCount() {
        return userRepository.getTotalUsersCount();
    }

    public int getTotalProblemsCount() {
        return problemRepository.getTotalProblemsCount();
    }

    public int getTotalSubmissionsCount() {
        return submissionRepository.getTotalSubmissionsCount();
    }
}
