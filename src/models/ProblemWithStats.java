package models;

/**
 * Wrapper class that combines Problem with its statistics
 * Used for displaying problems with solve count and average time
 */
public class ProblemWithStats {
    private final Problem problem;
    private final int solveCount;
    private final double averageTime;
    private final int likeCount;

    public ProblemWithStats(Problem problem, int solveCount, double averageTime, int likeCount) {
        if (problem == null) {
            throw new IllegalArgumentException("Problem cannot be null");
        }
        if (solveCount < 0) {
            throw new IllegalArgumentException("Solve count cannot be negative");
        }
        if (averageTime < 0) {
            throw new IllegalArgumentException("Average time cannot be negative");
        }
        if (likeCount < 0) {
            throw new IllegalArgumentException("Like count cannot be negative");
        }
        
        this.problem = problem;
        this.solveCount = solveCount;
        this.averageTime = averageTime;
        this.likeCount = likeCount;
    }

    // Getters
    public Problem getProblem() { return problem; }
    public int getSolveCount() { return solveCount; }
    public double getAverageTime() { return averageTime; }
    public int getLikeCount() { return likeCount; }

    @Override
    public String toString() {
        return "ProblemWithStats{" +
                "problem=" + problem +
                ", solveCount=" + solveCount +
                ", averageTime=" + String.format("%.2f", averageTime) +
                ", likeCount=" + likeCount +
                '}';
    }
}
