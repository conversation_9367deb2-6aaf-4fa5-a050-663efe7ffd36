package models;

import java.util.Objects;

/**
 * Represents a submission when a user solves a problem
 * Immutable class tracking solve time and calculated score
 */
public class Submission {
    private final String id;
    private final String userId;
    private final String problemId;
    private final long submissionTime;
    private final long timeTakenInSeconds;
    private final int calculatedScore;

    public Submission(String id, String userId, String problemId, long timeTakenInSeconds, int calculatedScore) {
        validateInput(id, userId, problemId, timeTakenInSeconds, calculatedScore);
        this.id = id;
        this.userId = userId;
        this.problemId = problemId;
        this.submissionTime = System.currentTimeMillis();
        this.timeTakenInSeconds = timeTakenInSeconds;
        this.calculatedScore = calculatedScore;
    }

    public Submission(String id, String userId, String problemId, long submissionTime, 
                     long timeTakenInSeconds, int calculatedScore) {
        validateInput(id, userId, problemId, timeTakenInSeconds, calculatedScore);
        this.id = id;
        this.userId = userId;
        this.problemId = problemId;
        this.submissionTime = submissionTime;
        this.timeTakenInSeconds = timeTakenInSeconds;
        this.calculatedScore = calculatedScore;
    }

    private void validateInput(String id, String userId, String problemId, 
                              long timeTakenInSeconds, int calculatedScore) {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("Submission ID cannot be null or empty");
        }
        if (userId == null || userId.trim().isEmpty()) {
            throw new IllegalArgumentException("User ID cannot be null or empty");
        }
        if (problemId == null || problemId.trim().isEmpty()) {
            throw new IllegalArgumentException("Problem ID cannot be null or empty");
        }
        if (timeTakenInSeconds < 0) {
            throw new IllegalArgumentException("Time taken cannot be negative");
        }
        if (calculatedScore < 0) {
            throw new IllegalArgumentException("Calculated score cannot be negative");
        }
    }

    // Getters
    public String getId() { return id; }
    public String getUserId() { return userId; }
    public String getProblemId() { return problemId; }
    public long getSubmissionTime() { return submissionTime; }
    public long getTimeTakenInSeconds() { return timeTakenInSeconds; }
    public int getCalculatedScore() { return calculatedScore; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Submission that = (Submission) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Submission{" +
                "id='" + id + '\'' +
                ", userId='" + userId + '\'' +
                ", problemId='" + problemId + '\'' +
                ", submissionTime=" + submissionTime +
                ", timeTakenInSeconds=" + timeTakenInSeconds +
                ", calculatedScore=" + calculatedScore +
                '}';
    }
}
