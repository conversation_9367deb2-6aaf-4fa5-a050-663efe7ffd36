package models;

import java.util.Objects;

/**
 * Represents a contestant/user in the hackathon platform
 * Immutable class with validation
 */
public class User {
    private final String id;
    private final String name;
    private final String department;
    private final long registrationTime;

    public User(String id, String name, String department) {
        validateInput(id, name, department);
        this.id = id;
        this.name = name;
        this.department = department;
        this.registrationTime = System.currentTimeMillis();
    }

    public User(String id, String name, String department, long registrationTime) {
        validateInput(id, name, department);
        this.id = id;
        this.name = name;
        this.department = department;
        this.registrationTime = registrationTime;
    }

    private void validateInput(String id, String name, String department) {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("User ID cannot be null or empty");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("User name cannot be null or empty");
        }
        if (department == null || department.trim().isEmpty()) {
            throw new IllegalArgumentException("Department cannot be null or empty");
        }
    }

    // Getters
    public String getId() { return id; }
    public String getName() { return name; }
    public String getDepartment() { return department; }
    public long getRegistrationTime() { return registrationTime; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return Objects.equals(id, user.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "User{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", department='" + department + '\'' +
                ", registrationTime=" + registrationTime +
                '}';
    }
}
