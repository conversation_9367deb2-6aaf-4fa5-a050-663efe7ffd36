package models;

import enums.Difficulty;
import java.util.Objects;
import java.util.Set;
import java.util.HashSet;

/**
 * Represents a problem in the hackathon platform
 * Immutable class following builder pattern for complex object creation
 */
public class Problem {
    private final String id;
    private final String name;
    private final String description;
    private final Set<String> tags;
    private final Difficulty difficulty;
    private final int score;
    private final long createdAt;

    private Problem(Builder builder) {
        this.id = builder.id;
        this.name = builder.name;
        this.description = builder.description;
        this.tags = new HashSet<>(builder.tags);
        this.difficulty = builder.difficulty;
        this.score = builder.score;
        this.createdAt = builder.createdAt;
    }

    // Getters
    public String getId() { return id; }
    public String getName() { return name; }
    public String getDescription() { return description; }
    public Set<String> getTags() { return new HashSet<>(tags); }
    public Difficulty getDifficulty() { return difficulty; }
    public int getScore() { return score; }
    public long getCreatedAt() { return createdAt; }

    // Builder Pattern
    public static class Builder {
        private String id;
        private String name;
        private String description;
        private Set<String> tags = new HashSet<>();
        private Difficulty difficulty;
        private int score;
        private long createdAt = System.currentTimeMillis();

        public Builder setId(String id) {
            this.id = id;
            return this;
        }

        public Builder setName(String name) {
            this.name = name;
            return this;
        }

        public Builder setDescription(String description) {
            this.description = description;
            return this;
        }

        public Builder setTags(Set<String> tags) {
            this.tags = new HashSet<>(tags);
            return this;
        }

        public Builder addTag(String tag) {
            this.tags.add(tag);
            return this;
        }

        public Builder setDifficulty(Difficulty difficulty) {
            this.difficulty = difficulty;
            return this;
        }

        public Builder setScore(int score) {
            this.score = score;
            return this;
        }

        public Builder setCreatedAt(long createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public Problem build() {
            validateBuilder();
            return new Problem(this);
        }

        private void validateBuilder() {
            if (id == null || id.trim().isEmpty()) {
                throw new IllegalArgumentException("Problem ID cannot be null or empty");
            }
            if (name == null || name.trim().isEmpty()) {
                throw new IllegalArgumentException("Problem name cannot be null or empty");
            }
            if (description == null || description.trim().isEmpty()) {
                throw new IllegalArgumentException("Problem description cannot be null or empty");
            }
            if (difficulty == null) {
                throw new IllegalArgumentException("Problem difficulty cannot be null");
            }
            if (score <= 0) {
                throw new IllegalArgumentException("Problem score must be positive");
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Problem problem = (Problem) o;
        return Objects.equals(id, problem.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Problem{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", difficulty=" + difficulty +
                ", score=" + score +
                ", tags=" + tags +
                '}';
    }
}
