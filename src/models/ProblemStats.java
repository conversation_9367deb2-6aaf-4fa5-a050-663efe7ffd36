package models;

/**
 * Represents statistics for a problem including solve count and average time
 * Mutable class for efficient updates during contest
 */
public class ProblemStats {
    private final String problemId;
    private int solveCount;
    private long totalTimeInSeconds;
    private int likeCount;

    public ProblemStats(String problemId) {
        if (problemId == null || problemId.trim().isEmpty()) {
            throw new IllegalArgumentException("Problem ID cannot be null or empty");
        }
        this.problemId = problemId;
        this.solveCount = 0;
        this.totalTimeInSeconds = 0;
        this.likeCount = 0;
    }

    // Getters
    public String getProblemId() { return problemId; }
    public int getSolveCount() { return solveCount; }
    public long getTotalTimeInSeconds() { return totalTimeInSeconds; }
    public int getLikeCount() { return likeCount; }

    public double getAverageTimeInSeconds() {
        return solveCount == 0 ? 0.0 : (double) totalTimeInSeconds / solveCount;
    }

    // Methods to update stats
    public void addSolve(long timeInSeconds) {
        if (timeInSeconds < 0) {
            throw new IllegalArgumentException("Time cannot be negative");
        }
        this.solveCount++;
        this.totalTimeInSeconds += timeInSeconds;
    }

    public void addLike() {
        this.likeCount++;
    }

    public void removeLike() {
        if (this.likeCount > 0) {
            this.likeCount--;
        }
    }

    @Override
    public String toString() {
        return "ProblemStats{" +
                "problemId='" + problemId + '\'' +
                ", solveCount=" + solveCount +
                ", averageTime=" + String.format("%.2f", getAverageTimeInSeconds()) +
                ", likeCount=" + likeCount +
                '}';
    }
}
