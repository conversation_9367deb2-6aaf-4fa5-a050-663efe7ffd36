import services.HackathonService;
import models.*;
import enums.*;
import java.util.*;

/**
 * Interactive mode for the PhonePe Hackathon Platform
 * Allows users to manually enter data and interact with the system
 */
public class InteractiveMode {
    private static final Scanner scanner = new Scanner(System.in);
    private static final HackathonService hackathonService = new HackathonService();

    public static void main(String[] args) {
        System.out.println("Welcome to PhonePe Hackathon Platform - Interactive Mode!");
        System.out.println("You can add problems, register users, solve problems, and more...\n");

        while (true) {
            showMenu();
            int choice = getIntInput("Enter your choice: ");
            
            try {
                switch (choice) {
                    case 1: addProblem(); break;
                    case 2: addUser(); break;
                    case 3: viewProblems(); break;
                    case 4: solveProblem(); break;
                    case 5: viewSolvedProblems(); break;
                    case 6: viewLeaderboard(); break;
                    case 7: viewTopProblems(); break;
                    case 8: viewStatistics(); break;
                    case 9: 
                        System.out.println("Thanks for using PhonePe Hackathon Platform! 👋");
                        return;
                    default:
                        System.out.println(" Invalid choice. Please try again.\n");
                }
            } catch (Exception e) {
                System.out.println(" Error: " + e.getMessage() + "\n");
            }
        }
    }

    private static void showMenu() {
        System.out.println("=== PhonePe Hackathon Platform ===");
        System.out.println("1. Add Problem");
        System.out.println("2. Register User");
        System.out.println("3. View Problems");
        System.out.println("4. Solve Problem");
        System.out.println("5. View Solved Problems");
        System.out.println("6. View Leaderboard");
        System.out.println("7. View Top Problems by Tag");
        System.out.println("8. View Statistics");
        System.out.println("9. Exit");
        System.out.println("=====================================");
    }

    private static void addProblem() {
        System.out.println("\nAdding New Problem");
        System.out.println("---------------------");
        
        String id = getStringInput("Problem ID: ");
        String name = getStringInput("Problem Name: ");
        String description = getStringInput("Problem Description: ");
        
        System.out.println("Available difficulties: EASY, MEDIUM, HARD");
        String difficultyStr = getStringInput("Difficulty: ").toUpperCase();
        Difficulty difficulty = Difficulty.valueOf(difficultyStr);
        
        int score = getIntInput("Score: ");
        
        System.out.println("Enter tags (comma-separated): ");
        String tagsInput = scanner.nextLine();
        Set<String> tags = new HashSet<>();
        if (!tagsInput.trim().isEmpty()) {
            for (String tag : tagsInput.split(",")) {
                tags.add(tag.trim());
            }
        }
        
        hackathonService.addProblem(id, name, description, tags, difficulty, score);
        System.out.println("Problem added successfully!\n");
    }

    private static void addUser() {
        System.out.println("\nRegistering New User");
        System.out.println("-----------------------");
        
        String id = getStringInput("User ID: ");
        String name = getStringInput("User Name: ");
        String department = getStringInput("Department: ");
        
        hackathonService.addUser(id, name, department);
        System.out.println("User registered successfully!\n");
    }

    private static void viewProblems() {
        System.out.println("\nAvailable Problems");
        System.out.println("--------------------");
        
        System.out.println("Filter options:");
        System.out.println("1. All problems");
        System.out.println("2. Filter by difficulty");
        System.out.println("3. Filter by tag");
        
        int filterChoice = getIntInput("Choose filter: ");
        Map<FilterCriteria, Object> filters = new HashMap<>();
        
        switch (filterChoice) {
            case 2:
                System.out.println("Available difficulties: EASY, MEDIUM, HARD");
                String diff = getStringInput("Enter difficulty: ").toUpperCase();
                filters.put(FilterCriteria.DIFFICULTY, Difficulty.valueOf(diff));
                break;
            case 3:
                String tag = getStringInput("Enter tag: ");
                filters.put(FilterCriteria.TAG, tag);
                break;
        }
        
        System.out.println("Sort by: SCORE_DESC, SCORE_ASC, DIFFICULTY_ASC, DIFFICULTY_DESC");
        String sortInput = getStringInput("Sort criteria (or press Enter for default): ");
        SortCriteria sortCriteria = null;
        if (!sortInput.trim().isEmpty()) {
            sortCriteria = SortCriteria.valueOf(sortInput.toUpperCase());
        }
        
        List<ProblemWithStats> problems = hackathonService.fetchProblems(
            filters.isEmpty() ? null : filters, sortCriteria);
        
        if (problems.isEmpty()) {
            System.out.println("No problems found with the given criteria.");
        } else {
            System.out.println("\nFound " + problems.size() + " problem(s):");
            for (ProblemWithStats p : problems) {
                System.out.println(" " + p.getProblem().getId() + " - " + p.getProblem().getName() +
                    " [" + p.getProblem().getDifficulty() + "] (Score: " + p.getProblem().getScore() + 
                    ", Solved by: " + p.getSolveCount() + " users)");
            }
        }
        System.out.println();
    }

    private static void solveProblem() {
        System.out.println("\n Solve Problem");
        System.out.println("----------------");
        
        String userId = getStringInput("Your User ID: ");
        String problemId = getStringInput("Problem ID to solve: ");
        int timeInSeconds = getIntInput("Time taken (in seconds): ");
        
        List<Problem> recommendations = hackathonService.solve(userId, problemId, timeInSeconds);
        
        System.out.println("🎉 Problem solved successfully!");
        
        if (!recommendations.isEmpty()) {
            System.out.println("\n💡 Recommended problems for you:");
            for (Problem p : recommendations) {
                System.out.println("🔸 " + p.getId() + " - " + p.getName() + 
                    " [" + p.getDifficulty() + "] (Score: " + p.getScore() + ")");
            }
        }
        System.out.println();
    }

    private static void viewSolvedProblems() {
        System.out.println("\n View Solved Problems");
        System.out.println("----------------------");
        
        String userId = getStringInput("User ID: ");
        
        List<ProblemWithStats> solvedProblems = hackathonService.fetchSolvedProblems(userId);
        
        if (solvedProblems.isEmpty()) {
            System.out.println("No problems solved yet by this user.");
        } else {
            System.out.println("Problems solved by user " + userId + ":");
            for (ProblemWithStats p : solvedProblems) {
                System.out.println(" " + p.getProblem().getId() + " - " + p.getProblem().getName() +
                    " [" + p.getProblem().getDifficulty() + "] (Score: " + p.getProblem().getScore() + ")");
            }
        }
        System.out.println();
    }

    private static void viewLeaderboard() {
        System.out.println("\n Current Leaderboard");
        System.out.println("---------------------");
        
        try {
            User leader = hackathonService.getLeader();
            System.out.println(" Current Leader: " + leader.getName() +
                " from " + leader.getDepartment() + " department");
        } catch (Exception e) {
            System.out.println("No submissions yet. Be the first to solve a problem!");
        }
        System.out.println();
    }

    private static void viewTopProblems() {
        System.out.println("\n Top Problems by Tag");
        System.out.println("---------------------");
        
        String tag = getStringInput("Enter tag: ");
        int count = getIntInput("How many top problems to show: ");
        
        List<ProblemWithStats> topProblems = hackathonService.getTopNProblems(tag, count);
        
        if (topProblems.isEmpty()) {
            System.out.println("No problems found with tag: " + tag);
        } else {
            System.out.println("Top " + topProblems.size() + " problems with tag '" + tag + "':");
            for (int i = 0; i < topProblems.size(); i++) {
                ProblemWithStats p = topProblems.get(i);
                System.out.println((i + 1) + ". " + p.getProblem().getName() + 
                    " (Likes: " + p.getLikeCount() + ", Score: " + p.getProblem().getScore() + ")");
            }
        }
        System.out.println();
    }

    private static void viewStatistics() {
        System.out.println("\n Platform Statistics");
        System.out.println("---------------------");
        System.out.println("Total Users: " + hackathonService.getTotalUsersCount());
        System.out.println("Total Problems: " + hackathonService.getTotalProblemsCount());
        System.out.println("Total Submissions: " + hackathonService.getTotalSubmissionsCount());
        System.out.println();
    }

    // Helper methods
    private static String getStringInput(String prompt) {
        System.out.print(prompt);
        return scanner.nextLine().trim();
    }

    private static int getIntInput(String prompt) {
        while (true) {
            try {
                System.out.print(prompt);
                return Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println(" Please enter a valid number.");
            }
        }
    }
}
