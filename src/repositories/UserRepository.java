package repositories;

import models.User;
import exceptions.CustomExceptions;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Repository for managing users in memory
 * Thread-safe implementation using ConcurrentHashMap
 */
public class UserRepository {
    private final Map<String, User> users;

    public UserRepository() {
        this.users = new ConcurrentHashMap<>();
    }

    /**
     * Add a new user to the repository
     */
    public void addUser(User user) {
        if (user == null) {
            throw new IllegalArgumentException("User cannot be null");
        }
        
        if (users.containsKey(user.getId())) {
            throw new CustomExceptions.UserAlreadyExistsException(
                "User with ID " + user.getId() + " already exists");
        }
        
        users.put(user.getId(), user);
    }

    /**
     * Get a user by ID
     */
    public User getUserById(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            throw new IllegalArgumentException("User ID cannot be null or empty");
        }
        
        User user = users.get(userId);
        if (user == null) {
            throw new CustomExceptions.UserNotFoundException(
                "User with ID " + userId + " not found");
        }
        
        return user;
    }

    /**
     * Get all users
     */
    public List<User> getAllUsers() {
        return new ArrayList<>(users.values());
    }

    /**
     * Get users by department
     */
    public List<User> getUsersByDepartment(String department) {
        if (department == null || department.trim().isEmpty()) {
            throw new IllegalArgumentException("Department cannot be null or empty");
        }
        
        return users.values().stream()
                .filter(u -> u.getDepartment().equalsIgnoreCase(department))
                .collect(Collectors.toList());
    }

    /**
     * Check if a user exists
     */
    public boolean userExists(String userId) {
        return userId != null && users.containsKey(userId);
    }

    /**
     * Get the total number of users
     */
    public int getTotalUsersCount() {
        return users.size();
    }

    /**
     * Get all unique departments
     */
    public Set<String> getAllDepartments() {
        return users.values().stream()
                .map(User::getDepartment)
                .collect(Collectors.toSet());
    }

    /**
     * Search users by name (case-insensitive partial match)
     */
    public List<User> searchUsersByName(String namePattern) {
        if (namePattern == null || namePattern.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        String pattern = namePattern.toLowerCase();
        return users.values().stream()
                .filter(u -> u.getName().toLowerCase().contains(pattern))
                .collect(Collectors.toList());
    }
}
