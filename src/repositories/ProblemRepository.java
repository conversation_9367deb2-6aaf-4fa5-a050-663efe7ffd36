package repositories;

import models.Problem;
import models.ProblemStats;
import enums.Difficulty;
import exceptions.CustomExceptions;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Repository for managing problems in memory
 * Thread-safe implementation using ConcurrentHashMap
 */
public class ProblemRepository {
    private final Map<String, Problem> problems;
    private final Map<String, ProblemStats> problemStats;

    public ProblemRepository() {
        this.problems = new ConcurrentHashMap<>();
        this.problemStats = new ConcurrentHashMap<>();
    }

    /**
     * Add a new problem to the repository
     */
    public void addProblem(Problem problem) {
        if (problem == null) {
            throw new IllegalArgumentException("Problem cannot be null");
        }
        
        if (problems.containsKey(problem.getId())) {
            throw new CustomExceptions.ProblemAlreadyExistsException(
                "Problem with ID " + problem.getId() + " already exists");
        }
        
        problems.put(problem.getId(), problem);
        problemStats.put(problem.getId(), new ProblemStats(problem.getId()));
    }

    /**
     * Get a problem by ID
     */
    public Problem getProblemById(String problemId) {
        if (problemId == null || problemId.trim().isEmpty()) {
            throw new IllegalArgumentException("Problem ID cannot be null or empty");
        }
        
        Problem problem = problems.get(problemId);
        if (problem == null) {
            throw new CustomExceptions.ProblemNotFoundException(
                "Problem with ID " + problemId + " not found");
        }
        
        return problem;
    }

    /**
     * Get all problems
     */
    public List<Problem> getAllProblems() {
        return new ArrayList<>(problems.values());
    }

    /**
     * Get problems filtered by difficulty
     */
    public List<Problem> getProblemsByDifficulty(Difficulty difficulty) {
        if (difficulty == null) {
            throw new IllegalArgumentException("Difficulty cannot be null");
        }
        
        return problems.values().stream()
                .filter(p -> p.getDifficulty() == difficulty)
                .collect(Collectors.toList());
    }

    /**
     * Get problems filtered by tag
     */
    public List<Problem> getProblemsByTag(String tag) {
        if (tag == null || tag.trim().isEmpty()) {
            throw new IllegalArgumentException("Tag cannot be null or empty");
        }
        
        return problems.values().stream()
                .filter(p -> p.getTags().contains(tag))
                .collect(Collectors.toList());
    }

    /**
     * Get problems filtered by score range
     */
    public List<Problem> getProblemsByScoreRange(int minScore, int maxScore) {
        if (minScore < 0 || maxScore < 0 || minScore > maxScore) {
            throw new IllegalArgumentException("Invalid score range");
        }
        
        return problems.values().stream()
                .filter(p -> p.getScore() >= minScore && p.getScore() <= maxScore)
                .collect(Collectors.toList());
    }

    /**
     * Get problem statistics
     */
    public ProblemStats getProblemStats(String problemId) {
        if (problemId == null || problemId.trim().isEmpty()) {
            throw new IllegalArgumentException("Problem ID cannot be null or empty");
        }
        
        ProblemStats stats = problemStats.get(problemId);
        if (stats == null) {
            throw new CustomExceptions.ProblemNotFoundException(
                "Problem stats for ID " + problemId + " not found");
        }
        
        return stats;
    }

    /**
     * Get all problem statistics
     */
    public Map<String, ProblemStats> getAllProblemStats() {
        return new HashMap<>(problemStats);
    }

    /**
     * Update problem statistics when a problem is solved
     */
    public void updateProblemStats(String problemId, long timeTakenInSeconds) {
        ProblemStats stats = problemStats.get(problemId);
        if (stats != null) {
            stats.addSolve(timeTakenInSeconds);
        }
    }

    /**
     * Add a like to a problem
     */
    public void addLikeToProblem(String problemId) {
        ProblemStats stats = problemStats.get(problemId);
        if (stats != null) {
            stats.addLike();
        }
    }

    /**
     * Remove a like from a problem
     */
    public void removeLikeFromProblem(String problemId) {
        ProblemStats stats = problemStats.get(problemId);
        if (stats != null) {
            stats.removeLike();
        }
    }

    /**
     * Check if a problem exists
     */
    public boolean problemExists(String problemId) {
        return problemId != null && problems.containsKey(problemId);
    }

    /**
     * Get the total number of problems
     */
    public int getTotalProblemsCount() {
        return problems.size();
    }

    /**
     * Get all unique tags from all problems
     */
    public Set<String> getAllTags() {
        return problems.values().stream()
                .flatMap(p -> p.getTags().stream())
                .collect(Collectors.toSet());
    }
}
