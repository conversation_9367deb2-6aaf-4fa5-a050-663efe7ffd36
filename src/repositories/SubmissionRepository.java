package repositories;

import models.Submission;
import exceptions.CustomExceptions;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Repository for managing submissions in memory
 * Thread-safe implementation using ConcurrentHashMap
 */
public class SubmissionRepository {
    private final Map<String, Submission> submissions;
    private final Map<String, Set<String>> userSubmissions; // userId -> Set of submissionIds
    private final Map<String, Set<String>> problemSubmissions; // problemId -> Set of submissionIds
    private final Map<String, Set<String>> userSolvedProblems; // userId -> Set of problemIds

    public SubmissionRepository() {
        this.submissions = new ConcurrentHashMap<>();
        this.userSubmissions = new ConcurrentHashMap<>();
        this.problemSubmissions = new ConcurrentHashMap<>();
        this.userSolvedProblems = new ConcurrentHashMap<>();
    }

    /**
     * Add a new submission to the repository
     */
    public void addSubmission(Submission submission) {
        if (submission == null) {
            throw new IllegalArgumentException("Submission cannot be null");
        }
        
        // Check if user has already solved this problem
        if (hasUserSolvedProblem(submission.getUserId(), submission.getProblemId())) {
            throw new CustomExceptions.ProblemAlreadySolvedException(
                "User " + submission.getUserId() + " has already solved problem " + submission.getProblemId());
        }
        
        submissions.put(submission.getId(), submission);
        
        // Update user submissions index
        userSubmissions.computeIfAbsent(submission.getUserId(), k -> ConcurrentHashMap.newKeySet())
                      .add(submission.getId());
        
        // Update problem submissions index
        problemSubmissions.computeIfAbsent(submission.getProblemId(), k -> ConcurrentHashMap.newKeySet())
                          .add(submission.getId());
        
        // Update user solved problems index
        userSolvedProblems.computeIfAbsent(submission.getUserId(), k -> ConcurrentHashMap.newKeySet())
                         .add(submission.getProblemId());
    }

    /**
     * Get a submission by ID
     */
    public Submission getSubmissionById(String submissionId) {
        if (submissionId == null || submissionId.trim().isEmpty()) {
            throw new IllegalArgumentException("Submission ID cannot be null or empty");
        }
        
        Submission submission = submissions.get(submissionId);
        if (submission == null) {
            throw new CustomExceptions.UserNotFoundException(
                "Submission with ID " + submissionId + " not found");
        }
        
        return submission;
    }

    /**
     * Get all submissions by a user
     */
    public List<Submission> getSubmissionsByUser(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            throw new IllegalArgumentException("User ID cannot be null or empty");
        }
        
        Set<String> submissionIds = userSubmissions.getOrDefault(userId, Collections.emptySet());
        return submissionIds.stream()
                .map(submissions::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * Get all submissions for a problem
     */
    public List<Submission> getSubmissionsByProblem(String problemId) {
        if (problemId == null || problemId.trim().isEmpty()) {
            throw new IllegalArgumentException("Problem ID cannot be null or empty");
        }
        
        Set<String> submissionIds = problemSubmissions.getOrDefault(problemId, Collections.emptySet());
        return submissionIds.stream()
                .map(submissions::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * Get all problems solved by a user
     */
    public Set<String> getProblemsSolvedByUser(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            throw new IllegalArgumentException("User ID cannot be null or empty");
        }
        
        return new HashSet<>(userSolvedProblems.getOrDefault(userId, Collections.emptySet()));
    }

    /**
     * Check if a user has solved a specific problem
     */
    public boolean hasUserSolvedProblem(String userId, String problemId) {
        if (userId == null || problemId == null) {
            return false;
        }
        
        Set<String> solvedProblems = userSolvedProblems.get(userId);
        return solvedProblems != null && solvedProblems.contains(problemId);
    }

    /**
     * Get total score for a user
     */
    public int getTotalScoreForUser(String userId) {
        return getSubmissionsByUser(userId).stream()
                .mapToInt(Submission::getCalculatedScore)
                .sum();
    }

    /**
     * Get all submissions
     */
    public List<Submission> getAllSubmissions() {
        return new ArrayList<>(submissions.values());
    }

    /**
     * Get submission count for a problem
     */
    public int getSubmissionCountForProblem(String problemId) {
        Set<String> submissionIds = problemSubmissions.get(problemId);
        return submissionIds != null ? submissionIds.size() : 0;
    }

    /**
     * Get total submissions count
     */
    public int getTotalSubmissionsCount() {
        return submissions.size();
    }

    /**
     * Get user rankings based on total score
     */
    public List<Map.Entry<String, Integer>> getUserRankings() {
        Map<String, Integer> userScores = new HashMap<>();
        
        for (String userId : userSubmissions.keySet()) {
            userScores.put(userId, getTotalScoreForUser(userId));
        }
        
        return userScores.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .collect(Collectors.toList());
    }
}
