package exceptions;

/**
 * Custom exceptions for the hackathon platform
 * Provides specific error handling for different scenarios
 */
public class CustomExceptions {

    /**
     * Exception thrown when a user is not found
     */
    public static class UserNotFoundException extends RuntimeException {
        public UserNotFoundException(String message) {
            super(message);
        }
    }

    /**
     * Exception thrown when a problem is not found
     */
    public static class ProblemNotFoundException extends RuntimeException {
        public ProblemNotFoundException(String message) {
            super(message);
        }
    }

    /**
     * Exception thrown when a user already exists
     */
    public static class UserAlreadyExistsException extends RuntimeException {
        public UserAlreadyExistsException(String message) {
            super(message);
        }
    }

    /**
     * Exception thrown when a problem already exists
     */
    public static class ProblemAlreadyExistsException extends RuntimeException {
        public ProblemAlreadyExistsException(String message) {
            super(message);
        }
    }

    /**
     * Exception thrown when a user tries to solve a problem they've already solved
     */
    public static class ProblemAlreadySolvedException extends RuntimeException {
        public ProblemAlreadySolvedException(String message) {
            super(message);
        }
    }

    /**
     * Exception thrown for invalid filter or sort operations
     */
    public static class InvalidOperationException extends RuntimeException {
        public InvalidOperationException(String message) {
            super(message);
        }
    }

    /**
     * Exception thrown when no data is available for requested operation
     */
    public static class NoDataAvailableException extends RuntimeException {
        public NoDataAvailableException(String message) {
            super(message);
        }
    }
}
