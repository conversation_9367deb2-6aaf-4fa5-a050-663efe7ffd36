import services.HackathonService;
import models.*;
import enums.*;
import strategies.*;
import java.util.*;

/**
 * Main class demonstrating the PhonePe Hackathon Platform
 * Showcases all mandatory and extension functionalities
 */
public class Main {
    private static final HackathonService hackathonService = new HackathonService();

    public static void main(String[] args) {
        System.out.println("=== PhonePe Hackathon Platform Demo ===\n");

        try {
            // Setup demo data
            setupDemoData();

            // Demonstrate all functionalities
            demonstrateAddProblem();
            demonstrateAddUser();
            demonstrateFetchProblems();
            demonstrateSolveProblem();
            demonstrateFetchSolvedProblems();
            demonstrateGetLeader();
            demonstrateGetTopNProblems();

            System.out.println("\n=== Demo completed successfully! ===");

        } catch (Exception e) {
            System.err.println("Demo failed with error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void setupDemoData() {
        System.out.println("1. Setting up demo data...\n");

        // Add sample problems
        addSampleProblems();

        // Add sample users
        addSampleUsers();

        // Add some likes to problems
        addSampleLikes();

        System.out.println("Demo data setup completed!\n");
    }

    private static void addSampleProblems() {
        // Easy problems
        hackathonService.addProblem("P001", "Two Sum",
            "Find two numbers that add up to target",
            Set.of("array", "hash-table"), Difficulty.EASY, 100);

        hackathonService.addProblem("P002", "Palindrome Check",
            "Check if a string is palindrome",
            Set.of("string", "two-pointers"), Difficulty.EASY, 80);

        // Medium problems
        hackathonService.addProblem("P003", "Binary Tree Traversal",
            "Implement inorder traversal",
            Set.of("tree", "recursion"), Difficulty.MEDIUM, 200);

        hackathonService.addProblem("P004", "Longest Substring",
            "Find longest substring without repeating characters",
            Set.of("string", "sliding-window"), Difficulty.MEDIUM, 250);

        // Hard problems
        hackathonService.addProblem("P005", "Merge K Sorted Lists",
            "Merge k sorted linked lists",
            Set.of("linked-list", "heap", "divide-conquer"), Difficulty.HARD, 400);

        hackathonService.addProblem("P006", "Word Ladder",
            "Transform one word to another",
            Set.of("graph", "bfs", "string"), Difficulty.HARD, 350);
    }

    private static void addSampleUsers() {
        hackathonService.addUser("U001", "Alice Johnson", "Engineering");
        hackathonService.addUser("U002", "Bob Smith", "Product");
        hackathonService.addUser("U003", "Charlie Brown", "Engineering");
        hackathonService.addUser("U004", "Diana Prince", "Design");
        hackathonService.addUser("U005", "Eve Wilson", "Engineering");
    }

    private static void addSampleLikes() {
        // Add likes to make some problems more popular
        for (int i = 0; i < 15; i++) hackathonService.addLikeToProblem("P001");
        for (int i = 0; i < 12; i++) hackathonService.addLikeToProblem("P003");
        for (int i = 0; i < 8; i++) hackathonService.addLikeToProblem("P004");
        for (int i = 0; i < 5; i++) hackathonService.addLikeToProblem("P002");
        for (int i = 0; i < 3; i++) hackathonService.addLikeToProblem("P005");
    }

    private static void demonstrateAddProblem() {
        System.out.println("2. Demonstrating addProblem()...");

        try {
            hackathonService.addProblem("P007", "Dynamic Programming Problem",
                "Solve using DP approach",
                Set.of("dynamic-programming", "optimization"), Difficulty.HARD, 500);
            System.out.println("✓ Successfully added new problem\n");
        } catch (Exception e) {
            System.err.println("✗ Error: " + e.getMessage() + "\n");
        }
    }

    private static void demonstrateAddUser() {
        System.out.println("3. Demonstrating addUser()...");

        try {
            hackathonService.addUser("U006", "Frank Miller", "Data Science");
            System.out.println("✓ Successfully added new user\n");
        } catch (Exception e) {
            System.err.println("✗ Error: " + e.getMessage() + "\n");
        }
    }

    private static void demonstrateFetchProblems() {
        System.out.println("4. Demonstrating fetchProblems()...");

        // Fetch all problems
        System.out.println("4.1. All problems:");
        List<ProblemWithStats> allProblems = hackathonService.fetchProblems(null, null);
        allProblems.forEach(System.out::println);

        // Filter by difficulty
        System.out.println("\n4.2. Easy problems only:");
        Map<FilterCriteria, Object> filters = new HashMap<>();
        filters.put(FilterCriteria.DIFFICULTY, Difficulty.EASY);
        List<ProblemWithStats> easyProblems = hackathonService.fetchProblems(filters, null);
        easyProblems.forEach(System.out::println);

        // Filter by tag and sort by score
        System.out.println("\n4.3. String problems sorted by score (descending):");
        filters.clear();
        filters.put(FilterCriteria.TAG, "string");
        List<ProblemWithStats> stringProblems = hackathonService.fetchProblems(filters, SortCriteria.SCORE_DESC);
        stringProblems.forEach(System.out::println);

        System.out.println();
    }

    private static void demonstrateSolveProblem() {
        System.out.println("5. Demonstrating solve()...");

        try {
            // User U001 solves some problems
            System.out.println("5.1. Alice solving problems:");
            List<Problem> recommendations1 = hackathonService.solve("U001", "P001", 120); // 2 minutes
            System.out.println("Recommendations after solving P001:");
            recommendations1.forEach(p -> System.out.println("  - " + p.getName()));

            List<Problem> recommendations2 = hackathonService.solve("U001", "P003", 300); // 5 minutes
            System.out.println("Recommendations after solving P003:");
            recommendations2.forEach(p -> System.out.println("  - " + p.getName()));

            // User U002 solves problems
            System.out.println("\n5.2. Bob solving problems:");
            hackathonService.solve("U002", "P001", 180); // 3 minutes
            hackathonService.solve("U002", "P002", 90);  // 1.5 minutes
            hackathonService.solve("U002", "P004", 450); // 7.5 minutes

            // User U003 solves problems
            System.out.println("\n5.3. Charlie solving problems:");
            hackathonService.solve("U003", "P005", 600); // 10 minutes
            hackathonService.solve("U003", "P001", 100); // 1.67 minutes

            System.out.println("✓ Successfully demonstrated problem solving with recommendations\n");
        } catch (Exception e) {
            System.err.println("✗ Error: " + e.getMessage() + "\n");
        }
    }

    private static void demonstrateFetchSolvedProblems() {
        System.out.println("6. Demonstrating fetchSolvedProblems()...");

        try {
            System.out.println("6.1. Problems solved by Alice (U001):");
            List<ProblemWithStats> aliceSolved = hackathonService.fetchSolvedProblems("U001");
            aliceSolved.forEach(p -> System.out.println("  - " + p.getProblem().getName() +
                " (Score: " + p.getProblem().getScore() + ")"));

            System.out.println("\n6.2. Problems solved by Bob (U002):");
            List<ProblemWithStats> bobSolved = hackathonService.fetchSolvedProblems("U002");
            bobSolved.forEach(p -> System.out.println("  - " + p.getProblem().getName() +
                " (Score: " + p.getProblem().getScore() + ")"));

            System.out.println("✓ Successfully fetched solved problems\n");
        } catch (Exception e) {
            System.err.println("✗ Error: " + e.getMessage() + "\n");
        }
    }

    private static void demonstrateGetLeader() {
        System.out.println("7. Demonstrating getLeader()...");

        try {
            User leader = hackathonService.getLeader();
            System.out.println("Current leader: " + leader.getName() +
                " from " + leader.getDepartment() + " department");
            System.out.println("✓ Successfully retrieved current leader\n");
        } catch (Exception e) {
            System.err.println("✗ Error: " + e.getMessage() + "\n");
        }
    }

    private static void demonstrateGetTopNProblems() {
        System.out.println("8. Demonstrating getTopNProblems()...");

        try {
            System.out.println("8.1. Top 3 most liked 'string' problems:");
            List<ProblemWithStats> topStringProblems = hackathonService.getTopNProblems("string", 3);
            topStringProblems.forEach(p -> System.out.println("  - " + p.getProblem().getName() +
                " (Likes: " + p.getLikeCount() + ")"));

            System.out.println("\n8.2. Top 5 most liked 'array' problems:");
            List<ProblemWithStats> topArrayProblems = hackathonService.getTopNProblems("array", 5);
            topArrayProblems.forEach(p -> System.out.println("  - " + p.getProblem().getName() +
                " (Likes: " + p.getLikeCount() + ")"));

            System.out.println("✓ Successfully retrieved top problems by tag\n");
        } catch (Exception e) {
            System.err.println("✗ Error: " + e.getMessage() + "\n");
        }
    }

    // Additional utility methods for demonstration
    private static void printStatistics() {
        System.out.println("=== Platform Statistics ===");
        System.out.println("Total Users: " + hackathonService.getTotalUsersCount());
        System.out.println("Total Problems: " + hackathonService.getTotalProblemsCount());
        System.out.println("Total Submissions: " + hackathonService.getTotalSubmissionsCount());
        System.out.println();
    }
}