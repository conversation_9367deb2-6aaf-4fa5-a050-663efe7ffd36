package tests;

import services.HackathonService;
import models.*;
import enums.*;
import exceptions.CustomExceptions;
import strategies.*;
import java.util.*;

/**
 * Comprehensive test suite for HackathonService
 * Tests all mandatory and extension functionalities
 */
public class HackathonServiceTest {
    private HackathonService hackathonService;
    private static int testsPassed = 0;
    private static int totalTests = 0;

    public static void main(String[] args) {
        HackathonServiceTest tester = new HackathonServiceTest();
        tester.runAllTests();
        
        System.out.println("\n=== Test Results ===");
        System.out.println("Tests Passed: " + testsPassed + "/" + totalTests);
        System.out.println("Success Rate: " + String.format("%.2f", (double) testsPassed / totalTests * 100) + "%");
        
        if (testsPassed == totalTests) {
            System.out.println("🎉 All tests passed!");
        } else {
            System.out.println("❌ Some tests failed. Please review the implementation.");
        }
    }

    public void runAllTests() {
        System.out.println("=== Running HackathonService Tests ===\n");
        
        // Test basic functionality
        testAddProblem();
        testAddUser();
        testFetchProblems();
        testSolveProblem();
        testFetchSolvedProblems();
        testGetLeader();
        testGetTopNProblems();
        
        // Test edge cases and error handling
        testErrorHandling();
        testFilteringAndSorting();
        testRecommendationSystem();
        testScoringStrategies();
        
        // Test concurrent operations
        testConcurrentOperations();
    }

    private void testAddProblem() {
        System.out.println("Testing addProblem()...");
        hackathonService = new HackathonService();
        
        // Test valid problem addition
        assertNoException(() -> {
            hackathonService.addProblem("P001", "Test Problem", "Description", 
                Set.of("test"), Difficulty.EASY, 100);
        }, "Should add valid problem successfully");
        
        // Test duplicate problem
        assertException(() -> {
            hackathonService.addProblem("P001", "Duplicate", "Description", 
                Set.of("test"), Difficulty.EASY, 100);
        }, CustomExceptions.ProblemAlreadyExistsException.class, "Should reject duplicate problem ID");
        
        // Test invalid inputs
        assertException(() -> {
            hackathonService.addProblem("", "Empty ID", "Description", 
                Set.of("test"), Difficulty.EASY, 100);
        }, IllegalArgumentException.class, "Should reject empty problem ID");
        
        assertException(() -> {
            hackathonService.addProblem("P002", "", "Description", 
                Set.of("test"), Difficulty.EASY, 100);
        }, IllegalArgumentException.class, "Should reject empty problem name");
        
        assertException(() -> {
            hackathonService.addProblem("P003", "Valid Name", "Description", 
                Set.of("test"), Difficulty.EASY, -10);
        }, IllegalArgumentException.class, "Should reject negative score");
        
        System.out.println("✓ addProblem() tests completed\n");
    }

    private void testAddUser() {
        System.out.println("Testing addUser()...");
        
        // Test valid user addition
        assertNoException(() -> {
            hackathonService.addUser("U001", "John Doe", "Engineering");
        }, "Should add valid user successfully");
        
        // Test duplicate user
        assertException(() -> {
            hackathonService.addUser("U001", "Jane Doe", "Product");
        }, CustomExceptions.UserAlreadyExistsException.class, "Should reject duplicate user ID");
        
        // Test invalid inputs
        assertException(() -> {
            hackathonService.addUser("", "Empty ID", "Engineering");
        }, IllegalArgumentException.class, "Should reject empty user ID");
        
        assertException(() -> {
            hackathonService.addUser("U002", "", "Engineering");
        }, IllegalArgumentException.class, "Should reject empty user name");
        
        assertException(() -> {
            hackathonService.addUser("U003", "Valid Name", "");
        }, IllegalArgumentException.class, "Should reject empty department");
        
        System.out.println("✓ addUser() tests completed\n");
    }

    private void testFetchProblems() {
        System.out.println("Testing fetchProblems()...");
        
        // Add test data
        setupTestData();
        
        // Test fetch all problems
        List<ProblemWithStats> allProblems = hackathonService.fetchProblems(null, null);
        assertTrue(allProblems.size() >= 3, "Should return all problems");
        
        // Test filtering by difficulty
        Map<FilterCriteria, Object> filters = new HashMap<>();
        filters.put(FilterCriteria.DIFFICULTY, Difficulty.EASY);
        List<ProblemWithStats> easyProblems = hackathonService.fetchProblems(filters, null);
        assertTrue(easyProblems.stream().allMatch(p -> p.getProblem().getDifficulty() == Difficulty.EASY),
            "Should return only easy problems");
        
        // Test filtering by tag
        filters.clear();
        filters.put(FilterCriteria.TAG, "array");
        List<ProblemWithStats> arrayProblems = hackathonService.fetchProblems(filters, null);
        assertTrue(arrayProblems.stream().allMatch(p -> p.getProblem().getTags().contains("array")),
            "Should return only problems with array tag");
        
        // Test sorting by score
        List<ProblemWithStats> sortedByScore = hackathonService.fetchProblems(null, SortCriteria.SCORE_DESC);
        assertTrue(isSortedByScoreDesc(sortedByScore), "Should be sorted by score in descending order");
        
        System.out.println("✓ fetchProblems() tests completed\n");
    }

    private void testSolveProblem() {
        System.out.println("Testing solve()...");
        
        // Test valid problem solving
        assertNoException(() -> {
            List<Problem> recommendations = hackathonService.solve("U001", "P001", 120);
            assertTrue(recommendations != null, "Should return recommendations");
            assertTrue(recommendations.size() <= 5, "Should return at most 5 recommendations");
        }, "Should solve problem successfully");
        
        // Test solving same problem again
        assertException(() -> {
            hackathonService.solve("U001", "P001", 150);
        }, CustomExceptions.ProblemAlreadySolvedException.class, "Should reject solving same problem twice");
        
        // Test invalid user
        assertException(() -> {
            hackathonService.solve("INVALID", "P001", 120);
        }, CustomExceptions.UserNotFoundException.class, "Should reject invalid user");
        
        // Test invalid problem
        assertException(() -> {
            hackathonService.solve("U001", "INVALID", 120);
        }, CustomExceptions.ProblemNotFoundException.class, "Should reject invalid problem");
        
        System.out.println("✓ solve() tests completed\n");
    }

    private void testFetchSolvedProblems() {
        System.out.println("Testing fetchSolvedProblems()...");
        
        // Test valid user
        List<ProblemWithStats> solvedProblems = hackathonService.fetchSolvedProblems("U001");
        assertTrue(solvedProblems.size() >= 1, "Should return solved problems");
        
        // Test user with no solved problems
        hackathonService.addUser("U999", "No Solver", "Test");
        List<ProblemWithStats> noSolved = hackathonService.fetchSolvedProblems("U999");
        assertTrue(noSolved.isEmpty(), "Should return empty list for user with no solved problems");
        
        // Test invalid user
        assertException(() -> {
            hackathonService.fetchSolvedProblems("INVALID");
        }, CustomExceptions.UserNotFoundException.class, "Should reject invalid user");
        
        System.out.println("✓ fetchSolvedProblems() tests completed\n");
    }

    private void testGetLeader() {
        System.out.println("Testing getLeader()...");
        
        // Test with submissions
        User leader = hackathonService.getLeader();
        assertTrue(leader != null, "Should return a leader");
        assertTrue(leader.getName() != null && !leader.getName().isEmpty(), "Leader should have a name");
        
        System.out.println("✓ getLeader() tests completed\n");
    }

    private void testGetTopNProblems() {
        System.out.println("Testing getTopNProblems()...");
        
        // Add likes to problems
        hackathonService.addLikeToProblem("P001");
        hackathonService.addLikeToProblem("P001");
        hackathonService.addLikeToProblem("P002");
        
        // Test valid tag
        List<ProblemWithStats> topProblems = hackathonService.getTopNProblems("array", 2);
        assertTrue(topProblems.size() <= 2, "Should return at most requested number");
        assertTrue(isSortedByLikesDesc(topProblems), "Should be sorted by likes in descending order");
        
        // Test invalid inputs
        assertException(() -> {
            hackathonService.getTopNProblems("", 5);
        }, IllegalArgumentException.class, "Should reject empty tag");
        
        assertException(() -> {
            hackathonService.getTopNProblems("array", 0);
        }, IllegalArgumentException.class, "Should reject zero count");
        
        assertException(() -> {
            hackathonService.getTopNProblems("array", -1);
        }, IllegalArgumentException.class, "Should reject negative count");
        
        System.out.println("✓ getTopNProblems() tests completed\n");
    }

    private void testErrorHandling() {
        System.out.println("Testing error handling...");

        // Test null inputs
        assertException(() -> {
            hackathonService.addProblem(null, "Name", "Desc", Set.of("tag"), Difficulty.EASY, 100);
        }, IllegalArgumentException.class, "Should handle null problem ID");

        assertException(() -> {
            hackathonService.addUser(null, "Name", "Dept");
        }, IllegalArgumentException.class, "Should handle null user ID");

        System.out.println("✓ Error handling tests completed\n");
    }

    private void testFilteringAndSorting() {
        System.out.println("Testing advanced filtering and sorting...");

        // Test multiple filters
        Map<FilterCriteria, Object> filters = new HashMap<>();
        filters.put(FilterCriteria.DIFFICULTY, Difficulty.MEDIUM);
        filters.put(FilterCriteria.TAG, "tree");

        List<ProblemWithStats> filtered = hackathonService.fetchProblems(filters, SortCriteria.SCORE_ASC);
        assertTrue(filtered.stream().allMatch(p ->
            p.getProblem().getDifficulty() == Difficulty.MEDIUM &&
            p.getProblem().getTags().contains("tree")), "Should apply multiple filters correctly");

        System.out.println("✓ Advanced filtering and sorting tests completed\n");
    }

    private void testRecommendationSystem() {
        System.out.println("Testing recommendation system...");

        // Solve a problem and check recommendations
        hackathonService.addUser("U_REC", "Rec User", "Test");
        List<Problem> recommendations = hackathonService.solve("U_REC", "P002", 100);

        assertTrue(recommendations != null, "Should return recommendations");
        assertTrue(recommendations.size() <= 5, "Should return at most 5 recommendations");
        assertTrue(recommendations.stream().noneMatch(p -> p.getId().equals("P002")),
            "Should not recommend the solved problem");

        System.out.println("✓ Recommendation system tests completed\n");
    }

    private void testScoringStrategies() {
        System.out.println("Testing scoring strategies...");

        // Test with different scoring strategies
        HackathonService simpleScoring = new HackathonService(
            ScoringStrategyFactory.StrategyType.SIMPLE,
            RecommendationStrategyFactory.StrategyType.TAG_BASED
        );

        simpleScoring.addProblem("PS001", "Simple Test", "Desc", Set.of("test"), Difficulty.EASY, 100);
        simpleScoring.addUser("US001", "Simple User", "Test");

        assertNoException(() -> {
            simpleScoring.solve("US001", "PS001", 60);
        }, "Should work with simple scoring strategy");

        System.out.println("✓ Scoring strategies tests completed\n");
    }

    private void testConcurrentOperations() {
        System.out.println("Testing concurrent operations...");

        // Test thread safety (basic test)
        assertNoException(() -> {
            for (int i = 0; i < 10; i++) {
                final int index = i;
                new Thread(() -> {
                    try {
                        hackathonService.addLikeToProblem("P001");
                    } catch (Exception e) {
                        // Expected for concurrent access
                    }
                }).start();
            }
            try {
                Thread.sleep(100); // Wait for threads to complete
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "Should handle concurrent operations");

        System.out.println("✓ Concurrent operations tests completed\n");
    }

    // Helper methods
    private void setupTestData() {
        hackathonService.addProblem("P002", "Array Problem", "Array description",
            Set.of("array", "easy"), Difficulty.EASY, 50);
        hackathonService.addProblem("P003", "Tree Problem", "Tree description",
            Set.of("tree", "medium"), Difficulty.MEDIUM, 150);
        hackathonService.addUser("U002", "Jane Smith", "Product");
    }

    private boolean isSortedByScoreDesc(List<ProblemWithStats> problems) {
        for (int i = 1; i < problems.size(); i++) {
            if (problems.get(i-1).getProblem().getScore() < problems.get(i).getProblem().getScore()) {
                return false;
            }
        }
        return true;
    }

    private boolean isSortedByLikesDesc(List<ProblemWithStats> problems) {
        for (int i = 1; i < problems.size(); i++) {
            if (problems.get(i-1).getLikeCount() < problems.get(i).getLikeCount()) {
                return false;
            }
        }
        return true;
    }

    // Test assertion methods
    private void assertNoException(Runnable action, String message) {
        totalTests++;
        try {
            action.run();
            testsPassed++;
            System.out.println("  ✓ " + message);
        } catch (Exception e) {
            System.err.println("  ✗ " + message + " - " + e.getMessage());
        }
    }

    private void assertException(Runnable action, Class<? extends Exception> expectedType, String message) {
        totalTests++;
        try {
            action.run();
            System.err.println("  ✗ " + message + " - Expected exception but none was thrown");
        } catch (Exception e) {
            if (expectedType.isInstance(e)) {
                testsPassed++;
                System.out.println("  ✓ " + message);
            } else {
                System.err.println("  ✗ " + message + " - Expected " + expectedType.getSimpleName() +
                    " but got " + e.getClass().getSimpleName());
            }
        }
    }

    private void assertTrue(boolean condition, String message) {
        totalTests++;
        if (condition) {
            testsPassed++;
            System.out.println("  ✓ " + message);
        } else {
            System.err.println("  ✗ " + message);
        }
    }
}
