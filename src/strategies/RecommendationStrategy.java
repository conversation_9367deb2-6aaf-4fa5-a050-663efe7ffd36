package strategies;

import models.Problem;
import models.ProblemStats;
import models.Submission;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Strategy interface for different recommendation algorithms
 * Allows extensible recommendation mechanisms
 */
public interface RecommendationStrategy {
    List<Problem> recommend(Problem solvedProblem, List<Problem> allProblems, 
                           List<Submission> userSubmissions, Map<String, ProblemStats> problemStats, int count);
}

/**
 * Tag-based recommendation strategy
 * Recommends problems with similar tags
 */
class TagBasedRecommendationStrategy implements RecommendationStrategy {
    @Override
    public List<Problem> recommend(Problem solvedProblem, List<Problem> allProblems, 
                                  List<Submission> userSubmissions, Map<String, ProblemStats> problemStats, int count) {
        Set<String> solvedProblemIds = userSubmissions.stream()
                .map(Submission::getProblemId)
                .collect(Collectors.toSet());

        Set<String> targetTags = solvedProblem.getTags();
        
        return allProblems.stream()
                .filter(p -> !solvedProblemIds.contains(p.getId()))
                .filter(p -> !p.getId().equals(solvedProblem.getId()))
                .sorted((p1, p2) -> {
                    int similarity1 = calculateTagSimilarity(targetTags, p1.getTags());
                    int similarity2 = calculateTagSimilarity(targetTags, p2.getTags());
                    if (similarity1 != similarity2) {
                        return Integer.compare(similarity2, similarity1); // Higher similarity first
                    }
                    return Integer.compare(p2.getScore(), p1.getScore()); // Higher score as tiebreaker
                })
                .limit(count)
                .collect(Collectors.toList());
    }

    private int calculateTagSimilarity(Set<String> tags1, Set<String> tags2) {
        Set<String> intersection = new HashSet<>(tags1);
        intersection.retainAll(tags2);
        return intersection.size();
    }
}

/**
 * Hybrid recommendation strategy combining multiple factors
 * Considers tags, difficulty progression, and popularity
 */
class HybridRecommendationStrategy implements RecommendationStrategy {
    private static final double TAG_WEIGHT = 0.4;
    private static final double DIFFICULTY_WEIGHT = 0.3;
    private static final double POPULARITY_WEIGHT = 0.3;

    @Override
    public List<Problem> recommend(Problem solvedProblem, List<Problem> allProblems, 
                                  List<Submission> userSubmissions, Map<String, ProblemStats> problemStats, int count) {
        Set<String> solvedProblemIds = userSubmissions.stream()
                .map(Submission::getProblemId)
                .collect(Collectors.toSet());

        return allProblems.stream()
                .filter(p -> !solvedProblemIds.contains(p.getId()))
                .filter(p -> !p.getId().equals(solvedProblem.getId()))
                .sorted((p1, p2) -> {
                    double score1 = calculateRecommendationScore(solvedProblem, p1, problemStats);
                    double score2 = calculateRecommendationScore(solvedProblem, p2, problemStats);
                    return Double.compare(score2, score1); // Higher score first
                })
                .limit(count)
                .collect(Collectors.toList());
    }

    private double calculateRecommendationScore(Problem solvedProblem, Problem candidate, 
                                              Map<String, ProblemStats> problemStats) {
        double tagScore = calculateTagSimilarity(solvedProblem.getTags(), candidate.getTags()) * TAG_WEIGHT;
        double difficultyScore = calculateDifficultyScore(solvedProblem, candidate) * DIFFICULTY_WEIGHT;
        double popularityScore = calculatePopularityScore(candidate, problemStats) * POPULARITY_WEIGHT;
        
        return tagScore + difficultyScore + popularityScore;
    }

    private double calculateTagSimilarity(Set<String> tags1, Set<String> tags2) {
        if (tags1.isEmpty() || tags2.isEmpty()) return 0.0;
        
        Set<String> intersection = new HashSet<>(tags1);
        intersection.retainAll(tags2);
        Set<String> union = new HashSet<>(tags1);
        union.addAll(tags2);
        
        return (double) intersection.size() / union.size();
    }

    private double calculateDifficultyScore(Problem solvedProblem, Problem candidate) {
        int solvedDifficultyLevel = getDifficultyLevel(solvedProblem.getDifficulty());
        int candidateDifficultyLevel = getDifficultyLevel(candidate.getDifficulty());
        
        // Prefer problems of same or slightly higher difficulty
        int diff = candidateDifficultyLevel - solvedDifficultyLevel;
        if (diff == 0) return 1.0;
        if (diff == 1) return 0.8;
        if (diff == -1) return 0.6;
        return 0.2;
    }

    private int getDifficultyLevel(enums.Difficulty difficulty) {
        switch (difficulty) {
            case EASY: return 1;
            case MEDIUM: return 2;
            case HARD: return 3;
            default: return 1;
        }
    }

    private double calculatePopularityScore(Problem candidate, Map<String, ProblemStats> problemStats) {
        ProblemStats stats = problemStats.get(candidate.getId());
        if (stats == null) return 0.0;
        
        // Normalize solve count (assuming max 1000 solves for normalization)
        double normalizedSolveCount = Math.min(stats.getSolveCount() / 1000.0, 1.0);
        return normalizedSolveCount;
    }
}


