package strategies;

/**
 * Factory class for creating scoring strategies
 */
public class ScoringStrategyFactory {
    public enum StrategyType {
        SIMPLE,
        TIME_BONUS
    }

    public static ScoringStrategy createStrategy(StrategyType type) {
        switch (type) {
            case SIMPLE:
                return new SimpleScoringStrategy();
            case TIME_BONUS:
                return new TimeBonusScoringStrategy();
            default:
                throw new IllegalArgumentException("Unknown scoring strategy type: " + type);
        }
    }
}
