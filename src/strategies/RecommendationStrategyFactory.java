package strategies;

/**
 * Factory class for creating recommendation strategies
 */
public class RecommendationStrategyFactory {
    public enum StrategyType {
        TAG_BASED,
        HYBRID
    }

    public static RecommendationStrategy createStrategy(StrategyType type) {
        switch (type) {
            case TAG_BASED:
                return new TagBasedRecommendationStrategy();
            case HYBRID:
                return new HybridRecommendationStrategy();
            default:
                throw new IllegalArgumentException("Unknown recommendation strategy type: " + type);
        }
    }
}
