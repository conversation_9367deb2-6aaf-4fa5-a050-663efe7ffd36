package strategies;

import models.Problem;

/**
 * Strategy interface for different scoring algorithms
 * Allows extensible scoring mechanisms
 */
public interface ScoringStrategy {
    int calculateScore(Problem problem, long timeTakenInSeconds);
}

/**
 * Simple scoring strategy that awards the base score of the problem
 */
class SimpleScoringStrategy implements ScoringStrategy {
    @Override
    public int calculateScore(Problem problem, long timeTakenInSeconds) {
        return problem.getScore();
    }
}

/**
 * Time-based scoring strategy that considers both score and time taken
 * Formula: baseScore * difficultyMultiplier * timeBonus
 * timeBonus decreases as time increases (max 2x bonus for very fast solutions)
 */
class TimeBonusScoringStrategy implements ScoringStrategy {
    private static final long MAX_BONUS_TIME = 300; // 5 minutes for max bonus
    private static final double MAX_BONUS_MULTIPLIER = 2.0;
    private static final long PENALTY_THRESHOLD = 3600; // 1 hour

    @Override
    public int calculateScore(Problem problem, long timeTakenInSeconds) {
        double baseScore = problem.getScore();
        double difficultyMultiplier = problem.getDifficulty().getMultiplier();
        
        double timeMultiplier = calculateTimeMultiplier(timeTakenInSeconds);
        
        return (int) Math.round(baseScore * difficultyMultiplier * timeMultiplier);
    }

    private double calculateTimeMultiplier(long timeTakenInSeconds) {
        if (timeTakenInSeconds <= MAX_BONUS_TIME) {
            // Linear bonus from 1.0 to MAX_BONUS_MULTIPLIER
            double bonusRatio = (double) (MAX_BONUS_TIME - timeTakenInSeconds) / MAX_BONUS_TIME;
            return 1.0 + (bonusRatio * (MAX_BONUS_MULTIPLIER - 1.0));
        } else if (timeTakenInSeconds <= PENALTY_THRESHOLD) {
            // No bonus, no penalty
            return 1.0;
        } else {
            // Penalty for very slow solutions
            double penaltyRatio = Math.min((double) (timeTakenInSeconds - PENALTY_THRESHOLD) / PENALTY_THRESHOLD, 0.5);
            return 1.0 - penaltyRatio;
        }
    }
}


