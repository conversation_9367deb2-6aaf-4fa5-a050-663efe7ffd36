package enums;

/**
 * Enum representing different filtering criteria for problems
 * Extensible design for adding new filtering options
 */
public enum FilterCriteria {
    DIFFICULTY,
    TAG,
    SCORE_RANGE,
    SOLVE_COUNT_RANGE,
    AVERAGE_TIME_RANGE,
    LIKE_COUNT_RANGE;

    public static FilterCriteria fromString(String criteria) {
        if (criteria == null) {
            throw new IllegalArgumentException("Filter criteria cannot be null");
        }
        
        try {
            return FilterCriteria.valueOf(criteria.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid filter criteria: " + criteria);
        }
    }
}
