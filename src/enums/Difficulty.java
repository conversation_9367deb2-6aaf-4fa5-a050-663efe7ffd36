package enums;

/**
 * Enum representing difficulty levels of problems
 * Includes multiplier for scoring calculations
 */
public enum Difficulty {
    EASY(1.0),
    MEDIUM(1.5),
    HARD(2.0);

    private final double multiplier;

    Difficulty(double multiplier) {
        this.multiplier = multiplier;
    }

    public double getMultiplier() {
        return multiplier;
    }

    public static Difficulty fromString(String difficulty) {
        if (difficulty == null) {
            throw new IllegalArgumentException("Difficulty cannot be null");
        }
        
        try {
            return Difficulty.valueOf(difficulty.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid difficulty: " + difficulty + 
                ". Valid values are: EASY, MEDIUM, HARD");
        }
    }
}
