package enums;

/**
 * Enum representing different sorting criteria for problems
 * Extensible design for adding new sorting options
 */
public enum SortCriteria {
    SCORE_ASC("score", true),
    SCORE_DESC("score", false),
    DIFFICULTY_ASC("difficulty", true),
    DIFFICULTY_DESC("difficulty", false),
    SOLVE_COUNT_ASC("solveCount", true),
    SOLVE_COUNT_DESC("solveCount", false),
    AVERAGE_TIME_ASC("averageTime", true),
    AVERAGE_TIME_DESC("averageTime", false),
    CREATED_TIME_ASC("createdTime", true),
    CREATED_TIME_DESC("createdTime", false),
    LIKE_COUNT_ASC("likeCount", true),
    LIKE_COUNT_DESC("likeCount", false);

    private final String field;
    private final boolean ascending;

    SortCriteria(String field, boolean ascending) {
        this.field = field;
        this.ascending = ascending;
    }

    public String getField() {
        return field;
    }

    public boolean isAscending() {
        return ascending;
    }

    public static SortCriteria fromString(String criteria) {
        if (criteria == null) {
            throw new IllegalArgumentException("Sort criteria cannot be null");
        }
        
        try {
            return SortCriteria.valueOf(criteria.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid sort criteria: " + criteria);
        }
    }
}
